<<<<<<< HEAD
[phases.setup]
nixPkgs = ["nodejs-18_x"]

[phases.install]
cmds = ["npm ci"]

[phases.build]
cmds = ["npm run build"]

[start]
cmd = "npm start"

=======
[phases.setup]
nixPkgs = ["nodejs-18_x", "pandoc"]

[phases.install]
cmds = ["npm ci"]

[phases.build]
cmds = ["npm run build"]

[start]
cmd = "npm start"

>>>>>>> 96bc5b4 (Restore original client-side Gemini prompt for better document structure)
[variables]
NODE_ENV = "production"
CACHE_BUSTER = "1"
