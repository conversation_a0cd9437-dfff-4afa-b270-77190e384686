# Database (Railway PostgreSQL)
DATABASE_URL="postgresql://username:password@localhost:5432/sow_generator"

# Storage (Railway Volume)
STORAGE_PATH="/app/storage"

# Authentication (integrate with Portal)
PORTAL_AUTH_URL="https://your-portal.railway.app"
PORTAL_API_KEY="your-portal-api-key"

# Next.js
NEXTAUTH_SECRET="your-nextauth-secret-here"
NEXTAUTH_URL="https://your-sow-generator.railway.app"

# API Configuration
NEXT_PUBLIC_API_URL="https://your-sow-generator.railway.app/api"
NEXT_PUBLIC_SOW_TEMPLATE_URL="https://your-sow-generator.railway.app/api/templates"

# File Upload Limits
MAX_FILE_SIZE=10485760  # 10MB
MAX_TEMPLATES_PER_USER=50
MAX_SOWS_PER_USER=1000

# AI APIs
OPENAI_API_KEY="your-openai-api-key-here"
# Required for smart template analysis and field detection
GEMINI_API_KEY=AIzaSyA96FY2bBKh1RtXZLEX9Xhxg169ZLET1ks

# Azure (optional)
NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING="your_azure_storage_connection_string_here"
NEXT_PUBLIC_AZURE_FUNCTIONS_URL="your_azure_functions_url_here"

# Environment
NODE_ENV="production"